import { Route, Routes } from "react-router";
import { LoadingSpinner } from "../components/ui/LoadingSpinner";
import React from "react";
import AdminHome from "../components/admin/AdminHome";
import Sidebar from "../components/admin/Sidebar";
import Container from "../components/ui/Container";
import AdminUsers from "../components/admin/AdminUsers";
import { AdminProvider } from "../context/AdminContext";
import AdminExercises from "../components/admin/AdminExercises";
import AdminCreateExercise from "../components/admin/AdminCreateExercise";
import AdminEditExercise from "../components/admin/AdminEditExercise";
import AdminViewExercise from "../components/admin/AdminViewExercise";
import AdminUserDetails from "../components/admin/AdminUserDetails";
import AdminVisualizations from "../components/admin/AdminVisualizations";
import AdminCreateVisualization from "../components/admin/AdminCreateVisualization";
import AdminEditVisualization from "../components/admin/AdminEditVisualization";
import AdminViewVisualization from "../components/admin/AdminViewVisualization";
import AdminOrganizations from "../components/admin/AdminOrganizations";
import AdminCreateOrganizations from "../components/admin/AdminCreateOrganizations";
import AdminActivityLogs from "../components/admin/AdminActivityLogs";
import AdminPeriodization from "../components/admin/AdminPeriodization";
import RichTextEditorTest from "../components/test/RichTextEditorTest";

export default function AdminDashboard() {
  return (
    <AdminProvider>
      <Container>
        <div className="flex flex-row h-full">
          <Sidebar />
          <div className="w-full mx-auto overflow-y-auto px-4 sm:px-6 lg:px-8 py-8">
            <Routes>
              <Route
                path="/"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminHome />
                  </React.Suspense>
                }
              />
              <Route
                path="/users"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminUsers />
                  </React.Suspense>
                }
              />
              <Route
                path="/users/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminUserDetails />
                  </React.Suspense>
                }
              />
              <Route
                path="/organizations"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminOrganizations />
                  </React.Suspense>
                }
              />
              <Route
                path="/organizations/create"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminCreateOrganizations />
                  </React.Suspense>
                }
              />
              <Route
                path="/exercises"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminExercises />
                  </React.Suspense>
                }
              />
              <Route
                path="/exercises/create"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminCreateExercise />
                  </React.Suspense>
                }
              />
              <Route
                path="/exercises/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminViewExercise />
                  </React.Suspense>
                }
              />
              <Route
                path="/exercises/edit/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminEditExercise />
                  </React.Suspense>
                }
              />
              <Route
                path="/visualizations"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminVisualizations />
                  </React.Suspense>
                }
              />
              <Route
                path="/visualizations/create"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminCreateVisualization />
                  </React.Suspense>
                }
              />
              <Route
                path="/visualizations/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminViewVisualization />
                  </React.Suspense>
                }
              />
              <Route
                path="/visualizations/edit/:id"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminEditVisualization />
                  </React.Suspense>
                }
              />
              <Route
                path="/activity-logs"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <AdminActivityLogs />
                  </React.Suspense>
                }
              />
              <Route
                path="/periodization"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <div className="text-center text-gray-500">
                      <AdminPeriodization />
                    </div>
                  </React.Suspense>
                }
              />
              <Route
                path="/test-editor"
                element={
                  <React.Suspense fallback={<LoadingSpinner />}>
                    <RichTextEditorTest />
                  </React.Suspense>
                }
              />
            </Routes>
          </div>
        </div>
      </Container>
    </AdminProvider>
  );
}
