import { useState, useEffect } from "react";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { Button } from "../ui/Button";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Column } from "../../types/components.types";
import { useAdmin } from "../../context/AdminContext";
import {
  Calendar,
  Play,
  BookOpen,
  Eye,
  BicepsFlexed,
  HeartPlus,
} from "lucide-react";
import {
  generatePeriodization,
  PeriodizationEntry,
} from "../../utils/periodization";
import { Label } from "../ui/Label";

const AdminPeriodization = () => {
  const {
    allExercises,
    allVisualizations,
    loading,
    error,
    clearError,
    fetchAllExercises,
    fetchAllVisualizations,
  } = useAdmin();
  const [weeks, setWeeks] = useState<number>(12);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [periodizationData, setPeriodizationData] = useState<
    PeriodizationEntry[]
  >([]);
  const [isGenerating, setIsGenerating] = useState(false);

  // Calculate end date when weeks or start date changes
  useEffect(() => {
    if (startDate && weeks > 0) {
      const start = new Date(startDate);
      const end = new Date(start);
      end.setDate(start.getDate() + weeks * 7 - 1);
      setEndDate(end.toISOString().split("T")[0]);
    }
  }, [startDate, weeks]);

  // Set default start date to today
  useEffect(() => {
    const today = new Date();
    setStartDate(today.toISOString().split("T")[0]);
    if (allExercises.length === 0 || allVisualizations.length === 0) {
      fetchAllExercises();
      fetchAllVisualizations();
    }
  }, []);

  const handleGeneratePeriodization = async () => {
    if (!startDate || weeks <= 0) return;

    setIsGenerating(true);
    const exercises = generatePeriodization(
      startDate,
      weeks,
      allExercises,
      allVisualizations
    );
    setPeriodizationData(exercises);
    setIsGenerating(false);
  };

  const columns: Column<PeriodizationEntry>[] = [
    {
      header: "Week",
      accessorKey: "week",
      cell: (entry) => (
        <div className="flex items-center">
          <Calendar className="h-4 w-4 text-blue-600 mr-2" />
          <span className="font-medium">Week {entry.week}</span>
        </div>
      ),
    },
    {
      header: "Assignment Date",
      accessorKey: "assignmentDate",
      cell: (entry) => (
        <span className="text-sm text-gray-900">
          {new Date(entry.assignmentDate).toLocaleDateString("en-US", {
            weekday: "short",
            year: "numeric",
            month: "short",
            day: "numeric",
          })}
        </span>
      ),
    },
    {
      header: "Type",
      accessorKey: "exerciseType",
      cell: (entry) => (
        <div className="flex items-center">
          {entry.exerciseType === "MT exercise" ? (
            <Label icon={BicepsFlexed} color="orange" text="MT Exercise" />
          ) : entry.exerciseType === "MW exercise" ? (
            <Label icon={HeartPlus} color="green" text="MW Exercise" />
          ) : (
            <Label icon={Eye} color="purple" text="Visualization" />
          )}
        </div>
      ),
    },
    {
      header: "Exercise/Visualization",
      accessorKey: "exerciseName",
      cell: (entry) => (
        <div>
          <p className="text-sm font-medium text-gray-900">
            {entry.exerciseName}
          </p>
          <p className="text-xs text-gray-500">ID: {entry.exerciseId}</p>
        </div>
      ),
    },
  ];

  if (loading.exercises || loading.visualizations) {
    return <LoadingSpinner />;
  }

  return (
    <div className="space-y-6">
      <PageHeader
        title="Periodization Testing"
        description="Test the periodization algorithm by generating assignment schedules"
      />

      {error && <ErrorMessage message={error} onDismiss={clearError} />}

      {/* Configuration Form */}
      <Card>
        <CardHeader title="Periodization Configuration" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label
                htmlFor="weeks"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Number of Weeks
              </label>
              <input
                type="number"
                id="weeks"
                min="1"
                max="52"
                value={weeks}
                onChange={(e) => setWeeks(parseInt(e.target.value) || 0)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>

            <div>
              <label
                htmlFor="startDate"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Start Date
              </label>
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              />
            </div>

            <div>
              <label
                htmlFor="endDate"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                End Date (Computed)
              </label>
              <input
                type="date"
                id="endDate"
                value={endDate}
                readOnly
                className="block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm sm:text-sm"
              />
            </div>
          </div>

          <div className="mt-6">
            <Button
              onClick={handleGeneratePeriodization}
              disabled={!startDate || weeks <= 0 || isGenerating}
              className="flex items-center"
            >
              <Play className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate Periodization"}
            </Button>
          </div>
        </div>
      </Card>

      {/* Available Resources Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-4">
          <div className="flex items-center">
            <BookOpen className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Exercises
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allExercises.length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Eye className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Available Visualizations
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {allVisualizations.length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Periodization Results */}
      {periodizationData.length > 0 && (
        <Card>
          <CardHeader
            title={`Periodization Schedule (${periodizationData.length} assignments)`}
          />
          <div className="p-6 pt-0">
            <DataTable
              columns={columns}
              data={periodizationData}
              className="w-full"
            />
          </div>
        </Card>
      )}

      {/* Empty State */}
      {periodizationData.length === 0 && startDate && weeks > 0 && (
        <Card className="p-8">
          <div className="text-center">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No periodization generated
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Click "Generate Periodization" to create an assignment schedule.
            </p>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AdminPeriodization;
