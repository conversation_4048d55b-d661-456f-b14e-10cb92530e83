import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PageHeader } from "../ui/PageHeader";
import { Card, CardHeader } from "../ui/Card";
import { DataTable } from "../ui/DataTable";
import { LoadingSpinner } from "../ui/LoadingSpinner";
import { ErrorMessage } from "../ui/ErrorMessage";
import { Button } from "../ui/Button";
import { Column } from "../../types/components.types";
import {
  Organization,
  OrganizationMember,
} from "../../types/api/organizations.types";
import {
  Building2,
  Users,
  UserCheck,
  ArrowLeft,
  Mail,
  Calendar,
  Edit,
  Trash,
  Plus,
} from "lucide-react";
import {
  getOrganizationById,
  removeOrganizationMember,
} from "../../api/organizations";
import { formatDate } from "../../utils/dateUtils";

const AdminOrganizationDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (id) {
      fetchOrganizationDetails();
    }
  }, [id]);

  const fetchOrganizationDetails = async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);
      const data = await getOrganizationById(id);
      setOrganization(data);
    } catch (err) {
      setError("Failed to load organization details");
      console.error("Error fetching organization:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveMember = async (
    userId: string,
    memberType: "coach" | "coachee"
  ) => {
    if (!id) return;

    if (
      window.confirm(
        "Are you sure you want to remove this member from the organization?"
      )
    ) {
      try {
        await removeOrganizationMember(id, userId);
        fetchOrganizationDetails(); // Refresh data
      } catch (error) {
        console.error("Error removing member:", error);
        setError("Failed to remove member");
      }
    }
  };

  const coachColumns: Column<OrganizationMember>[] = [
    {
      header: "Coach",
      accessorKey: "firstName",
      cell: (member) => (
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
            <UserCheck className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">
              {member.firstName} {member.lastName}
            </p>
            <p className="text-xs text-gray-500 flex items-center">
              <Mail className="h-3 w-3 mr-1" />
              {member.email}
            </p>
          </div>
        </div>
      ),
    },
    {
      header: "Role",
      accessorKey: "role",
      cell: (member) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {member.role}
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (member) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/users/${member.id}`)}
          >
            View Details
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleRemoveMember(member.id, "coach")}
            className="text-red-600 hover:text-red-800"
          >
            <Trash className="h-3 w-3 mr-1" />
            Remove
          </Button>
        </div>
      ),
    },
  ];

  const coacheeColumns: Column<OrganizationMember>[] = [
    {
      header: "Coachee",
      accessorKey: "firstName",
      cell: (member) => (
        <div className="flex items-center">
          <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
            <Users className="h-4 w-4 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">
              {member.firstName} {member.lastName}
            </p>
            <p className="text-xs text-gray-500 flex items-center">
              <Mail className="h-3 w-3 mr-1" />
              {member.email}
            </p>
          </div>
        </div>
      ),
    },
    {
      header: "Role",
      accessorKey: "role",
      cell: (member) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {member.role}
        </span>
      ),
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: (member) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate(`/dashboard/users/${member.id}`)}
          >
            View Details
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleRemoveMember(member.id, "coachee")}
            className="text-red-600 hover:text-red-800"
          >
            <Trash className="h-3 w-3 mr-1" />
            Remove
          </Button>
        </div>
      ),
    },
  ];

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error) {
    return <ErrorMessage message={error} />;
  }

  if (!organization) {
    return <ErrorMessage message="Organization not found" />;
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="outline"
        onClick={() => navigate("/dashboard/organizations")}
        className="mb-4"
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back to Organizations
      </Button>

      <PageHeader
        title={organization.name}
        description="Organization details and member management"
        actionButton={{
          label: "Edit Organization",
          onClick: () => navigate(`/dashboard/organizations/${id}/edit`),
        }}
      />

      {/* Organization Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-4">
          <div className="flex items-center">
            <Building2 className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Organization</p>
              <p className="text-lg font-semibold text-gray-900">
                {organization.name}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">Total Coaches</p>
              <p className="text-lg font-semibold text-gray-900">
                {organization.coachMemberships?.length || 0}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-600">
                Total Coachees
              </p>
              <p className="text-lg font-semibold text-gray-900">
                {organization.coacheeMemberships?.length || 0}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Organization Info */}
      <Card>
        <CardHeader title="Organization Information" />
        <div className="p-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Created
              </label>
              <p className="mt-1 text-sm text-gray-900 flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                {formatDate(organization.createdAt)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Last Updated
              </label>
              <p className="mt-1 text-sm text-gray-900 flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                {formatDate(organization.updatedAt)}
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Coaches Table */}
      <Card>
        <CardHeader
          title="Coaches"
          action={
            <Button
              size="sm"
              onClick={() =>
                navigate(`/dashboard/organizations/${id}/add-coach`)
              }
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Coach
            </Button>
          }
        />
        <div className="p-6 pt-0">
          {organization.coachMemberships &&
          organization.coachMemberships.length > 0 ? (
            <DataTable
              columns={coachColumns}
              data={organization.coachMemberships}
              className="w-full"
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No coaches assigned to this organization
            </div>
          )}
        </div>
      </Card>

      {/* Coachees Table */}
      <Card>
        <CardHeader
          title="Coachees"
          action={
            <Button
              size="sm"
              onClick={() =>
                navigate(`/dashboard/organizations/${id}/add-coachee`)
              }
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Coachee
            </Button>
          }
        />
        <div className="p-6 pt-0">
          {organization.coacheeMemberships &&
          organization.coacheeMemberships.length > 0 ? (
            <DataTable
              columns={coacheeColumns}
              data={organization.coacheeMemberships}
              className="w-full"
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              No coachees assigned to this organization
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default AdminOrganizationDetails;
