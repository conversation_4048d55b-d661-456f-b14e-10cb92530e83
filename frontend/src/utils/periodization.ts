import { Exercise } from "../types/api/exercises.types";

export type PeriodizationEntry = {
  week: number;
  assignmentDate: string;
  exerciseType: "MT exercise" | "MW exercise" | "visualization";
  exerciseName: string;
  exerciseId: string | number;
};

export const generatePeriodization = (
  startDate: string,
  weeks: number,
  allExercises: Exercise[],
  allVisualizations: any[]
) => {
  const entries: PeriodizationEntry[] = [];
  const start = new Date(startDate);

  const mtExercises = allExercises.filter((e) =>
    e.id.toLowerCase().startsWith("mt")
  );
  const mwExercises = allExercises.filter((e) =>
    e.id.toLowerCase().startsWith("mw")
  );

  for (let week = 1; week <= weeks; week++) {
    const assignmentDate = new Date(start);
    assignmentDate.setDate(start.getDate() + (week - 1) * 7);

    // Set 1 mt and 1 mw exercise every week
    if (week <= mtExercises.length) {
      entries.push({
        week,
        assignmentDate: assignmentDate.toISOString().split("T")[0],
        exerciseType: "MT exercise",
        exerciseName: mtExercises[week - 1].name,
        exerciseId: mtExercises[week - 1].id,
      });
    }
    if (week <= mwExercises.length) {
      entries.push({
        week,
        assignmentDate: assignmentDate.toISOString().split("T")[0],
        exerciseType: "MW exercise",
        exerciseName: mwExercises[week - 1].name,
        exerciseId: mwExercises[week - 1].id,
      });
    }
    if (week <= allVisualizations.length) {
      entries.push({
        week,
        assignmentDate: assignmentDate.toISOString().split("T")[0],
        exerciseType: "visualization",
        exerciseName: allVisualizations[week - 1].title,
        exerciseId: allVisualizations[week - 1].id,
      });
    }
  }

  return entries;
};
